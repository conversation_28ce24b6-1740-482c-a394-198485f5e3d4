{"_name_or_path": "Qwen/Qwen2-VL-2B-Instruct", "architectures": ["Qwen2VLForConditionalGeneration"], "attention_dropout": 0.0, "bos_token_id": 151643, "eos_token_id": 151645, "hidden_act": "silu", "hidden_size": 1536, "image_token_id": 151655, "initializer_range": 0.02, "intermediate_size": 8960, "max_position_embeddings": 32768, "max_window_layers": 28, "model_type": "qwen2_vl", "num_attention_heads": 12, "num_hidden_layers": 28, "num_key_value_heads": 2, "rms_norm_eps": 1e-06, "rope_scaling": {"mrope_section": [16, 24, 24], "type": "mrope"}, "rope_theta": 1000000.0, "sliding_window": 32768, "tie_word_embeddings": true, "tokenizer_model_max_length": 4096, "torch_dtype": "bfloat16", "transformers_version": "4.45.0.dev0", "use_cache": false, "use_sliding_window": false, "video_token_id": 151656, "vision_config": {"hidden_size": 1536, "in_chans": 3, "model_type": "qwen2_vl", "spatial_patch_size": 14}, "vision_end_token_id": 151653, "vision_start_token_id": 151652, "vision_token_id": 151654, "vocab_size": 151936}